<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cesium Earth - 类似Google Earth</title>
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Cesium.js"></script>
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="src2/main.css">
    <style>
        
    </style>
</head>
<body>
    <!-- 顶部工具栏 -->
    <div class="top-toolbar">
        <div class="logo">
            <i class="fas fa-globe"></i>
            <span>Cesium Earth</span>
        </div>

        <div class="toolbar-buttons">
            <button class="toolbar-btn">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </button>
            <button class="toolbar-btn">
                <i class="fas fa-map-marked-alt"></i>
                <span>地点</span>
            </button>
            <button class="toolbar-btn">
                <i class="fas fa-route"></i>
                <span>路径</span>
            </button>
            <button class="toolbar-btn">
                <i class="fas fa-ruler"></i>
                <span>测量</span>
            </button>
            <button class="toolbar-btn">
                <i class="fas fa-camera"></i>
                <span>截图</span>
            </button>
        </div>

        <div class="search-container">
            <input type="text" class="search-box" placeholder="搜索地点、坐标或地址...">
            <button class="search-btn">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>

    <!-- 左侧面板 -->
    <div class="left-panel">


        <!-- 测量工具 -->
        <div class="panel-section">
            <div class="panel-header" onclick="togglePanel('measure')">
                <span><i class="fas fa-ruler"></i> 测量工具</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="panel-content" id="measure">
                <div class="tool-item" data-action="measure.distance" onclick="toggleToolSettings('measureDistanceSettings')">
                    <i class="fas fa-ruler layer-icon"></i>
                    <span class="layer-name">距离测量</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-settings" id="measureDistanceSettings">
                    <div class="settings-content">
                        <div class="setting-group">
                            <label class="setting-label">测量精度</label>
                            <select class="setting-select" id="distancePrecision">
                                <option value="1">1位小数</option>
                                <option value="2" selected>2位小数</option>
                                <option value="3">3位小数</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">单位</label>
                            <select class="setting-select" id="distanceUnit">
                                <option value="m" selected>米 (m)</option>
                                <option value="km">千米 (km)</option>
                                <option value="ft">英尺 (ft)</option>
                            </select>
                        </div>
                        <div class="setting-buttons">
                            <button class="setting-btn" onclick="startMeasure('distance')">开始测量</button>
                            <button class="setting-btn secondary" onclick="clearMeasure()">清除</button>
                        </div>
                    </div>
                </div>

                <div class="tool-item" data-action="measure.area" onclick="toggleToolSettings('measureAreaSettings')">
                    <i class="fas fa-draw-polygon layer-icon"></i>
                    <span class="layer-name">面积测量</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-settings" id="measureAreaSettings">
                    <div class="settings-content">
                        <div class="setting-group">
                            <label class="setting-label">测量精度</label>
                            <select class="setting-select" id="areaPrecision">
                                <option value="1">1位小数</option>
                                <option value="2" selected>2位小数</option>
                                <option value="3">3位小数</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">单位</label>
                            <select class="setting-select" id="areaUnit">
                                <option value="m²" selected>平方米 (m²)</option>
                                <option value="km²">平方千米 (km²)</option>
                                <option value="ha">公顷 (ha)</option>
                            </select>
                        </div>
                        <div class="setting-buttons">
                            <button class="setting-btn" onclick="startMeasure('area')">开始测量</button>
                            <button class="setting-btn secondary" onclick="clearMeasure()">清除</button>
                        </div>
                    </div>
                </div>

                <div class="tool-item" data-action="measure.height" onclick="toggleToolSettings('measureHeightSettings')">
                    <i class="fas fa-mountain layer-icon"></i>
                    <span class="layer-name">高度测量</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-settings" id="measureHeightSettings">
                    <div class="settings-content">
                        <div class="setting-group">
                            <label class="setting-label">测量模式</label>
                            <select class="setting-select" id="heightMode">
                                <option value="terrain" selected>地形高度</option>
                                <option value="absolute">绝对高度</option>
                                <option value="relative">相对高度</option>
                            </select>
                        </div>
                        <div class="setting-buttons">
                            <button class="setting-btn" onclick="startMeasure('height')">开始测量</button>
                            <button class="setting-btn secondary" onclick="clearMeasure()">清除</button>
                        </div>
                    </div>
                </div>

                <div class="tool-item" data-action="measure.space" onclick="toggleToolSettings('measureSpaceSettings')">
                    <i class="fas fa-cube layer-icon"></i>
                    <span class="layer-name">空间距离</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-settings" id="measureSpaceSettings">
                    <div class="settings-content">
                        <div class="setting-group">
                            <label class="setting-label">计算方式</label>
                            <select class="setting-select" id="spaceMode">
                                <option value="straight" selected>直线距离</option>
                                <option value="ground">地表距离</option>
                            </select>
                        </div>
                        <div class="setting-buttons">
                            <button class="setting-btn" onclick="startMeasure('space')">开始测量</button>
                            <button class="setting-btn secondary" onclick="clearMeasure()">清除</button>
                        </div>
                    </div>
                </div>

                <div class="tool-item tool-clear" data-action="measure.clear">
                    <i class="fas fa-trash layer-icon"></i>
                    <span class="layer-name">清除所有测量</span>
                    <div class="tool-status"></div>
                </div>
            </div>
        </div>

        <!-- 搜索定位 -->
        <div class="panel-section">
            <div class="panel-header" onclick="togglePanel('search')">
                <span><i class="fas fa-search"></i> 搜索定位</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="panel-content" id="search">
                <div class="search-panel">
                    <div class="search-input-group">
                        <input type="text" id="panelSearchInput" class="panel-search-input" placeholder="输入地点名称或坐标...">
                        <button class="panel-search-btn" data-action="search.location">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="tool-item" data-action="search.coordinate">
                    <i class="fas fa-crosshairs layer-icon"></i>
                    <span class="layer-name">坐标定位</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-item" data-action="search.history">
                    <i class="fas fa-history layer-icon"></i>
                    <span class="layer-name">搜索历史</span>
                    <div class="tool-status"></div>
                </div>
            </div>
        </div>

        <!-- 地形开挖 -->
        <div class="panel-section">
            <div class="panel-header" onclick="togglePanel('terrainDig')">
                <span><i class="fas fa-hammer"></i> 地形开挖</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="panel-content" id="terrainDig">
                <div class="tool-item" data-action="terrain.dig" onclick="toggleToolSettings('terrainDigSettings')">
                    <i class="fas fa-hammer layer-icon"></i>
                    <span class="layer-name">地形开挖</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-settings" id="terrainDigSettings">
                    <div class="settings-content">
                        <div class="setting-group">
                            <label class="setting-label">开挖深度：<span class="range-value" id="depthValue">30米</span></label>
                            <input type="range" class="setting-range" id="digDepth" min="0" max="1000" value="30"
                                   oninput="updateRangeValue('depthValue', this.value + '米')">
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">开挖体积：<span class="range-value" id="volumeValue">0立方米</span></label>
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">底部纹理</label>
                            <select class="setting-select" id="bottomTexture">
                                <option value="src/images/ter_analysis/excavationregion_side.jpg">纹理1</option>
                                <option value="src/images/ter_analysis/excavationregion_top.jpg" selected>纹理2</option>
                            </select>
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">侧面纹理</label>
                            <select class="setting-select" id="sideTexture">
                                <option value="src/images/ter_analysis/excavationregion_top.jpg">纹理1</option>
                                <option value="src/images/ter_analysis/excavationregion_side.jpg" selected>纹理2</option>
                            </select>
                        </div>
                        <div class="setting-buttons">
                            <button class="setting-btn" onclick="startTerrainDig()">开始绘制</button>
                            <button class="setting-btn secondary" onclick="clearTerrainDig()">清除</button>
                            <button class="setting-btn danger" onclick="exitTerrainDig()">退出</button>
                        </div>
                    </div>
                </div>


            </div>
        </div>

        <!-- 剖面分析 -->
        <div class="panel-section">
            <div class="panel-header" onclick="togglePanel('profile')">
                <span><i class="fas fa-chart-line"></i> 剖面分析</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="panel-content" id="profile">
                <div class="tool-item" data-action="terrain.profile" onclick="toggleToolSettings('profileSettings')">
                    <i class="fas fa-chart-line layer-icon"></i>
                    <span class="layer-name">剖面分析</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-settings" id="profileSettings">
                    <div class="settings-content">
                        <div class="setting-group">
                            <label class="setting-label">起点坐标</label>
                            <div id="startCoord">点击地图选择起点</div>
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">终点坐标</label>
                            <div id="endCoord">点击地图选择终点</div>
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">剖面长度</label>
                            <div id="profileLength">-</div>
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">采样点数：<span class="range-value" id="sampleValue">100</span></label>
                            <input type="range" class="setting-range" id="sampleCount" min="50" max="500" value="100"
                                   oninput="updateRangeValue('sampleValue', this.value)">
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">分析选项</label>
                            <div style="margin: 5px 0;">
                                <input type="checkbox" id="showTerrain" checked> 显示地形线
                            </div>
                            <div style="margin: 5px 0;">
                                <input type="checkbox" id="interpolate" checked> 插值处理
                            </div>
                        </div>
                        <div class="setting-buttons">
                            <button class="setting-btn" onclick="startProfile()">绘制剖面线</button>
                            <button class="setting-btn secondary" onclick="clearProfile()">清除</button>
                            <button class="setting-btn danger" onclick="exitProfile()">退出分析</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 标记管理 -->
        <div class="panel-section">
            <div class="panel-header" onclick="togglePanel('markers')">
                <span><i class="fas fa-map-marker-alt"></i> 标记管理</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="panel-content" id="markers">
                <div class="tool-item" data-action="marker.add">
                    <i class="fas fa-plus layer-icon"></i>
                    <span class="layer-name">添加标记</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-item" data-action="marker.list">
                    <i class="fas fa-list layer-icon"></i>
                    <span class="layer-name">标记列表</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-item" data-action="marker.edit">
                    <i class="fas fa-edit layer-icon"></i>
                    <span class="layer-name">编辑标记</span>
                    <div class="tool-status"></div>
                </div>
            </div>
        </div>

        <!-- 书签管理 -->
        <div class="panel-section">
            <div class="panel-header" onclick="togglePanel('bookmark')">
                <span><i class="fas fa-bookmark"></i> 书签管理</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="panel-content" id="bookmark">
                <div class="tool-item" data-action="bookmark.add" onclick="toggleToolSettings('bookmarkSettings')">
                    <i class="fas fa-plus layer-icon"></i>
                    <span class="layer-name">书签管理</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-settings" id="bookmarkSettings">
                    <div class="settings-content">
                        <div class="setting-group">
                            <label class="setting-label">书签名称</label>
                            <input type="text" class="setting-input" id="bookmarkNameInput" placeholder="输入书签名称">
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">书签描述</label>
                            <input type="text" class="setting-input" id="bookmarkDescInput" placeholder="输入书签描述（可选）">
                        </div>
                        <div class="setting-buttons">
                            <button class="setting-btn" onclick="addBookmark()">添加书签</button>
                            <button class="setting-btn secondary" onclick="showBookmarks()">显示书签</button>
                        </div>
                        <div class="setting-group">
                            <label class="setting-label">已保存的书签</label>
                            <div id="bookmarkList" style="max-height: 150px; overflow-y: auto; margin-top: 5px;">
                                <!-- 书签列表将动态生成 -->
                                <div style="color: #95a5a6; font-size: 11px; text-align: center; padding: 10px;">
                                    暂无书签
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>





        <!-- 三维显示 -->
        <div class="panel-section">
            <div class="panel-header" onclick="togglePanel('3d')">
                <span><i class="fas fa-cube"></i> 三维显示</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="panel-content" id="3d">
                <div class="tool-item" data-action="3d.buildings">
                    <i class="fas fa-building layer-icon"></i>
                    <span class="layer-name">建筑物显示</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-item" data-action="3d.models">
                    <i class="fas fa-cube layer-icon"></i>
                    <span class="layer-name">模型加载</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-item" data-action="3d.effects">
                    <i class="fas fa-magic layer-icon"></i>
                    <span class="layer-name">效果设置</span>
                    <div class="tool-status"></div>
                </div>
            </div>
        </div>

        <!-- 打印功能 -->
        <div class="panel-section">
            <div class="panel-header" onclick="togglePanel('print')">
                <span><i class="fas fa-camera"></i> 打印功能</span>
                <i class="fas fa-chevron-down"></i>
            </div>
            <div class="panel-content" id="print">
                <div class="tool-item" data-action="system.print">
                    <i class="fas fa-camera layer-icon"></i>
                    <span class="layer-name">截图</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-item" data-action="system.export">
                    <i class="fas fa-download layer-icon"></i>
                    <span class="layer-name">导出数据</span>
                    <div class="tool-status"></div>
                </div>
                <div class="tool-item" data-action="print.settings">
                    <i class="fas fa-cog layer-icon"></i>
                    <span class="layer-name">打印设置</span>
                    <div class="tool-status"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主视图区域 -->
    <div class="main-view">
        <iframe id="cesiumFrame" src="/cesium"
                style="width: 100%; height: 100%; border: none;"
                onload="initCommunication()">
        </iframe>
        <!-- 加载状态指示器 -->
        <div id="loadingIndicator" class="loading-indicator">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在加载Cesium地球...</div>
        </div>
    </div>



    <!-- 迷你地图 -->
    <div class="mini-map" id="miniMap">
        <div class="mini-map-header">
            <span>概览地图</span>
            <button class="mini-map-toggle" onclick="toggleMiniMap()" title="收起/展开">
                <i class="fas fa-chevron-up" id="miniMapToggleIcon"></i>
            </button>
        </div>
        <div class="mini-map-content" id="miniMapContent">
            <div id="miniMapContainer" style="width: 100%; height: 120px; background: #1a1a1a; position: relative;">
                <!-- 概览地图容器 -->
                <div class="mini-map-placeholder">
                    <i class="fas fa-map" style="font-size: 24px; color: #3498db; margin-bottom: 5px;"></i>
                    <div style="font-size: 10px; color: #95a5a6;">点击初始化概览地图</div>
                </div>
                <!-- 当前视野框 -->
                <div class="viewport-indicator" id="viewportIndicator"></div>
            </div>
            <div class="mini-map-controls">
                <button class="mini-control-btn" onclick="initOverviewMap()" title="初始化概览地图">
                    <i class="fas fa-play"></i>
                </button>
                <button class="mini-control-btn" onclick="resetOverviewMap()" title="重置视野">
                    <i class="fas fa-home"></i>
                </button>
                <button class="mini-control-btn" onclick="syncOverviewMap()" title="同步主地图">
                    <i class="fas fa-sync"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="bottom-status">
        <!-- 左侧：坐标信息 -->
        <div class="status-left">
            <span>🌐 <span id="longitude">138.7874°</span></span>
            <span>🌐 <span id="latitude">36.1398°</span></span>
        </div>

        <!-- 中间：比例尺和高度 -->
        <div class="status-center">
            <span>📏 <span id="scaleText">25.2 km</span></span>
            <span>⛰️ <span id="altitude">1384m</span></span>
        </div>

        <!-- 右侧：坐标系和版权 -->
        <div class="status-right">
            <select id="crsSelector">
                <option value="WGS84">WGS84</option>
                <option value="CGCS2000">CGCS2000</option>
                <option value="BD09">BD09</option>
                <option value="GCJ02">GCJ02</option>
            </select>
            <span style="font-size: 9px; opacity: 0.7;">© Cesium Earth</span>
        </div>
    </div>

    <script>
        // 通信管理器
        class CommunicationManager {
            constructor() {
                this.messageQueue = [];
                this.isReady = false;
                this.messageId = 0;
                this.pendingMessages = new Map();
                this.setupMessageListener();
            }

            setupMessageListener() {
                window.addEventListener('message', (event) => {
                    if (event.source !== document.getElementById('cesiumFrame').contentWindow) {
                        return;
                    }

                    const { type, data, id, error } = event.data;

                    switch (type) {
                        case 'READY':
                            this.handleReady();
                            break;
                        case 'RESPONSE':
                            this.handleResponse(id, data, error);
                            break;
                        case 'STATUS_UPDATE':
                            this.handleStatusUpdate(data);
                            break;
                        case 'TOOL_STATE_CHANGE':
                            this.handleToolStateChange(data);
                            break;
                    }
                });
            }

            handleReady() {
                console.log('🌍 Cesium iframe 已准备就绪');
                this.isReady = true;
                this.hideLoadingIndicator();
                this.processMessageQueue();
            }

            handleResponse(id, data, error) {
                const pendingMessage = this.pendingMessages.get(id);
                if (pendingMessage) {
                    if (error) {
                        console.error('命令执行失败:', error);
                        pendingMessage.reject(new Error(error));
                    } else {
                        pendingMessage.resolve(data);
                    }
                    this.pendingMessages.delete(id);
                }
            }

            handleStatusUpdate(data) {
                // 更新状态栏信息
                if (data.longitude !== undefined) {
                    const lonText = Math.abs(data.longitude).toFixed(4) + '°' + (data.longitude >= 0 ? 'E' : 'W');
                    document.getElementById('longitude').textContent = lonText;
                }
                if (data.latitude !== undefined) {
                    const latText = Math.abs(data.latitude).toFixed(4) + '°' + (data.latitude >= 0 ? 'N' : 'S');
                    document.getElementById('latitude').textContent = latText;
                }
                if (data.altitude !== undefined) {
                    const altText = data.altitude >= 1000 ?
                        (data.altitude / 1000).toFixed(1) + 'km' :
                        Math.round(data.altitude) + 'm';
                    document.getElementById('altitude').textContent = altText;
                }
                if (data.scale !== undefined) {
                    document.getElementById('scaleText').textContent = data.scale;
                }
            }

            handleToolStateChange(data) {
                // 更新工具状态
                const { tool, state, message } = data;
                this.updateToolStatus(tool, state, message);
            }

            sendCommand(module, method, params = {}) {
                return new Promise((resolve, reject) => {
                    const id = ++this.messageId;
                    const message = {
                        type: 'FUNCTION_CALL',
                        module: module,
                        method: method,
                        params: params,
                        id: id
                    };

                    this.pendingMessages.set(id, { resolve, reject });

                    if (this.isReady) {
                        this.sendMessage(message);
                    } else {
                        this.messageQueue.push(message);
                    }
                });
            }

            sendMessage(message) {
                const iframe = document.getElementById('cesiumFrame');
                if (iframe && iframe.contentWindow) {
                    iframe.contentWindow.postMessage(message, '*');
                }
            }

            processMessageQueue() {
                while (this.messageQueue.length > 0) {
                    const message = this.messageQueue.shift();
                    this.sendMessage(message);
                }
            }

            hideLoadingIndicator() {
                const indicator = document.getElementById('loadingIndicator');
                if (indicator) {
                    indicator.style.opacity = '0';
                    setTimeout(() => {
                        indicator.style.display = 'none';
                    }, 300);
                }
            }

            updateToolStatus(tool, state, message = '') {
                const toolElement = document.querySelector(`[data-action="${tool}"]`);
                if (toolElement) {
                    const statusElement = toolElement.querySelector('.tool-status');
                    if (statusElement) {
                        statusElement.className = `tool-status ${state}`;
                        statusElement.textContent = message;
                    }

                    // 更新工具项状态
                    toolElement.classList.remove('active');
                    if (state === 'active') {
                        toolElement.classList.add('active');
                    }
                }
            }
        }



        // 工具功能处理器
        class ToolHandler {
            constructor(communicationManager) {
                this.comm = communicationManager;
                this.setupEventListeners();
            }

            setupEventListeners() {
                // 工具项点击事件
                document.querySelectorAll('.tool-item[data-action]').forEach(item => {
                    item.addEventListener('click', (e) => {
                        const action = item.getAttribute('data-action');
                        this.handleToolAction(action, item);
                    });
                });



                // 面板搜索功能
                const panelSearchInput = document.getElementById('panelSearchInput');
                const panelSearchBtn = document.querySelector('.panel-search-btn');

                if (panelSearchInput) {
                    panelSearchInput.addEventListener('keypress', (e) => {
                        if (e.key === 'Enter') {
                            this.handleSearch(panelSearchInput.value);
                        }
                    });
                }

                if (panelSearchBtn) {
                    panelSearchBtn.addEventListener('click', () => {
                        this.handleSearch(panelSearchInput.value);
                    });
                }

                // 顶部搜索功能
                document.querySelector('.search-box').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleSearch(e.target.value);
                    }
                });

                document.querySelector('.search-btn').addEventListener('click', () => {
                    const searchValue = document.querySelector('.search-box').value;
                    this.handleSearch(searchValue);
                });

                // 控制按钮事件
                document.querySelectorAll('.control-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.handleControlButton(btn);
                    });
                });

                // 工具栏按钮事件
                document.querySelectorAll('.toolbar-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        this.handleToolbarButton(btn);
                    });
                });
            }

            async handleToolAction(action, element) {
                console.log('🔧 工具操作:', action);

                try {
                    const [category, tool] = action.split('.');

                    switch (category) {
                        case 'measure':
                            await this.handleMeasureTool(tool);
                            break;
                        case 'search':
                            await this.handleSearchTool(tool);
                            break;
                        case 'terrain':
                            await this.handleTerrainTool(tool);
                            break;
                        case 'marker':
                            await this.handleMarkerTool(tool);
                            break;
                        case 'bookmark':
                            await this.handleBookmarkTool(tool);
                            break;

                        case '3d':
                            await this.handle3DTool(tool);
                            break;
                        case 'system':
                            await this.handleSystemTool(tool);
                            break;
                        default:
                            console.warn('未知工具类别:', category);
                    }
                } catch (error) {
                    console.error('工具操作失败:', error);
                    this.comm.updateToolStatus(action, 'error', '操作失败');
                }
            }

            async handleMeasureTool(tool) {
                const toolMap = {
                    'distance': { module: 'measureUI', method: 'measureLineSpace' },
                    'area': { module: 'measureUI', method: 'measureAreaSpace' },
                    'height': { module: 'measureUI', method: 'altitude' },
                    'space': { module: 'measureUI', method: 'groundMeasureLineSpace' },
                    'triangle': { module: 'measureUI', method: 'Triangle' },
                    'angle': { module: 'measureUI', method: 'measureAngle' },
                    'clear': { module: 'measureUI', method: 'Clear' }
                };

                const config = toolMap[tool];
                if (config) {
                    await this.comm.sendCommand(config.module, config.method);
                    this.comm.updateToolStatus(`measure.${tool}`, 'active', '已激活');
                }
            }

            async handleSearchTool(tool) {
                switch (tool) {
                    case 'coordinate':
                        // 坐标定位功能 - 打开坐标输入对话框
                        const coordinates = prompt('请输入坐标（格式：经度,纬度 或 经度 纬度）：');
                        if (coordinates) {
                            await this.comm.sendCommand('searchUI', 'performSearch', {
                                searchText: coordinates.trim()
                            });
                            this.comm.updateToolStatus('search.coordinate', 'active', '坐标定位完成');
                        }
                        break;
                    case 'history':
                        // 搜索历史功能 - 显示搜索历史
                        await this.comm.sendCommand('searchUI', 'showSearchHistory');
                        this.comm.updateToolStatus('search.history', 'active', '搜索历史已显示');
                        break;
                }
            }

            async handleTerrainTool(tool) {
                const toolMap = {
                    'dig': { module: 'terrainDigUI', method: 'startDrawing' },
                    'profile': { module: 'profileAnalysisUI', method: 'startProfile' }
                };

                const config = toolMap[tool];
                if (config) {
                    await this.comm.sendCommand(config.module, config.method);
                    this.comm.updateToolStatus(`terrain.${tool}`, 'active', '已激活');
                }
            }

            async handleMarkerTool(tool) {
                const toolMap = {
                    'add': { module: 'addMarkerUI', method: 'startAddMarker' },
                    'list': { module: 'addMarkerUI', method: 'showMarkerList' },
                    'edit': { module: 'addMarkerUI', method: 'editMode' }
                };

                const config = toolMap[tool];
                if (config) {
                    await this.comm.sendCommand(config.module, config.method);
                    this.comm.updateToolStatus(`marker.${tool}`, 'active', '已激活');
                }
            }

            async handleBookmarkTool(tool) {
                const toolMap = {
                    'add': { module: 'bookmarkUI', method: 'addBookmark' },
                    'show': { module: 'bookmarkUI', method: 'showBookmarks' },
                    'list': { module: 'bookmarkUI', method: 'getAllBookmarks' }
                };

                const config = toolMap[tool];
                if (config) {
                    if (tool === 'add') {
                        // 添加书签需要用户输入
                        const name = prompt('请输入书签名称：');
                        if (name && name.trim()) {
                            await this.comm.sendCommand(config.module, config.method, { name: name.trim() });
                            this.comm.updateToolStatus(`bookmark.${tool}`, 'active', '书签已添加');
                        }
                    } else {
                        await this.comm.sendCommand(config.module, config.method);
                        this.comm.updateToolStatus(`bookmark.${tool}`, 'active', '已激活');
                    }
                }
            }



            async handle3DTool(tool) {
                const toolMap = {
                    'buildings': { module: 'building3DUI', method: 'toggleBuildings' },
                    'models': { module: 'building3DUI', method: 'loadModels' },
                    'effects': { module: 'building3DUI', method: 'showEffects' }
                };

                const config = toolMap[tool];
                if (config) {
                    await this.comm.sendCommand(config.module, config.method);
                    this.comm.updateToolStatus(`3d.${tool}`, 'active', '已激活');
                }
            }

            async handleSystemTool(tool) {
                const toolMap = {
                    'print': { module: 'printUI', method: 'takeScreenshot' },
                    'export': { module: 'printUI', method: 'exportData' },
                    'settings': { module: 'printUI', method: 'showSettings' }
                };

                const config = toolMap[tool];
                if (config) {
                    await this.comm.sendCommand(config.module, config.method);
                    this.comm.updateToolStatus(`system.${tool}`, 'active', '已激活');
                }
            }



            async handleSearch(searchText) {
                if (!searchText.trim()) return;

                console.log('🔍 搜索:', searchText);
                await this.comm.sendCommand('searchUI', 'performSearch', {
                    searchText: searchText.trim()
                });
            }

            handleControlButton(btn) {
                const title = btn.getAttribute('title');
                console.log('🎮 控制按钮:', title);

                // 可以在这里添加其他控制按钮的处理逻辑
            }

            handleToolbarButton(btn) {
                const text = btn.textContent.trim();
                console.log('🔧 工具栏按钮:', text);

                // 根据按钮文本执行相应操作
                switch (text) {
                    case '首页':
                        this.comm.sendCommand('viewer', 'flyHome');
                        break;
                    case '测量':
                        // 激活测量面板
                        togglePanel('measure');
                        break;
                    case '截图':
                        this.handleSystemTool('print');
                        break;
                }
            }
        }

        // 全局变量
        let communicationManager;
        let toolHandler;

        // iframe加载完成后初始化通信
        function initCommunication() {
            console.log('🚀 初始化通信管理器...');
            communicationManager = new CommunicationManager();

            // 等待通信就绪后初始化工具处理器
            setTimeout(() => {
                if (communicationManager.isReady) {
                    toolHandler = new ToolHandler(communicationManager);
                    console.log('🔧 工具处理器已初始化');
                } else {
                    // 如果还未就绪，延迟初始化
                    const checkReady = setInterval(() => {
                        if (communicationManager.isReady) {
                            toolHandler = new ToolHandler(communicationManager);
                            console.log('🔧 工具处理器已初始化（延迟）');
                            clearInterval(checkReady);
                        }
                    }, 500);
                }
            }, 1000);
        }

        // 面板切换功能
        function togglePanel(panelId) {
            const panel = document.getElementById(panelId);
            const header = panel.previousElementSibling;
            const icon = header.querySelector('.fa-chevron-down, .fa-chevron-up');

            if (panel.classList.contains('active')) {
                panel.classList.remove('active');
                icon.className = 'fas fa-chevron-down';
            } else {
                panel.classList.add('active');
                icon.className = 'fas fa-chevron-up';
            }
        }

        // 工具设置面板管理
        function toggleToolSettings(settingsId) {
            // 关闭其他所有设置面板
            document.querySelectorAll('.tool-settings').forEach(panel => {
                if (panel.id !== settingsId) {
                    panel.classList.remove('active');
                }
            });

            // 切换当前设置面板
            const panel = document.getElementById(settingsId);
            if (panel) {
                panel.classList.toggle('active');
            }
        }

        // 更新范围滑块显示值
        function updateRangeValue(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value;
            }
        }

        // 测量功能
        function startMeasure(type) {
            console.log('🔧 开始测量:', type);
            const precision = document.getElementById(type + 'Precision')?.value || 2;
            const unit = document.getElementById(type + 'Unit')?.value || 'm';

            if (communicationManager) {
                communicationManager.sendCommand('measureUI', 'measure' + type.charAt(0).toUpperCase() + type.slice(1), {
                    precision: precision,
                    unit: unit
                });
            }
        }

        function clearMeasure() {
            console.log('🧹 清除测量');
            if (communicationManager) {
                communicationManager.sendCommand('measureUI', 'Clear');
            }
        }



        function startTerrainDig() {
            console.log('⛏️ 开始地形开挖');
            const depth = document.getElementById('digDepth').value;
            const bottomTexture = document.getElementById('bottomTexture').value;
            const sideTexture = document.getElementById('sideTexture').value;

            if (communicationManager) {
                communicationManager.sendCommand('terrainDigUI', 'startDrawing', {
                    depth: depth,
                    bottomTexture: bottomTexture,
                    sideTexture: sideTexture
                });
            }
        }

        function clearTerrainDig() {
            console.log('🧹 清除地形开挖');
            if (communicationManager) {
                communicationManager.sendCommand('terrainDigUI', 'clear');
            }
        }

        function exitTerrainDig() {
            console.log('🚪 退出地形开挖');
            if (communicationManager) {
                communicationManager.sendCommand('terrainDigUI', 'exit');
            }
        }



        function startProfile() {
            console.log('📊 开始剖面分析');
            const sampleCount = document.getElementById('sampleCount').value;
            const showTerrain = document.getElementById('showTerrain').checked;
            const interpolate = document.getElementById('interpolate').checked;

            if (communicationManager) {
                communicationManager.sendCommand('profileAnalysisUI', 'startProfile', {
                    sampleCount: sampleCount,
                    showTerrain: showTerrain,
                    interpolate: interpolate
                });
            }
        }

        function clearProfile() {
            console.log('🧹 清除剖面分析');
            if (communicationManager) {
                communicationManager.sendCommand('profileAnalysisUI', 'clear');
            }
        }

        function exitProfile() {
            console.log('🚪 退出剖面分析');
            if (communicationManager) {
                communicationManager.sendCommand('profileAnalysisUI', 'exit');
            }
        }

        // 书签管理功能
        function addBookmark() {
            const name = document.getElementById('bookmarkNameInput').value.trim();
            const description = document.getElementById('bookmarkDescInput').value.trim();

            if (!name) {
                alert('请输入书签名称');
                return;
            }

            console.log('📖 添加书签:', name);
            if (communicationManager) {
                communicationManager.sendCommand('bookmarkUI', 'addBookmark', {
                    name: name,
                    description: description
                }).then(() => {
                    // 清空输入框
                    document.getElementById('bookmarkNameInput').value = '';
                    document.getElementById('bookmarkDescInput').value = '';
                    // 刷新书签列表
                    refreshBookmarkList();
                });
            }
        }

        function showBookmarks() {
            console.log('📖 显示书签');
            if (communicationManager) {
                communicationManager.sendCommand('bookmarkUI', 'showBookmarks');
            }
        }

        function deleteBookmark(bookmarkId) {
            if (confirm('确定要删除这个书签吗？')) {
                console.log('📖 删除书签:', bookmarkId);
                if (communicationManager) {
                    communicationManager.sendCommand('bookmarkUI', 'deleteBookmark', {
                        id: bookmarkId
                    }).then(() => {
                        refreshBookmarkList();
                    });
                }
            }
        }

        function editBookmark(bookmarkId) {
            const newName = prompt('请输入新的书签名称：');
            if (newName && newName.trim()) {
                console.log('📖 编辑书签:', bookmarkId, newName);
                if (communicationManager) {
                    communicationManager.sendCommand('bookmarkUI', 'editBookmark', {
                        id: bookmarkId,
                        name: newName.trim()
                    }).then(() => {
                        refreshBookmarkList();
                    });
                }
            }
        }

        function refreshBookmarkList() {
            console.log('📖 刷新书签列表');
            if (communicationManager) {
                communicationManager.sendCommand('bookmarkUI', 'getAllBookmarks').then((bookmarks) => {
                    // 更新书签列表显示
                    updateBookmarkListDisplay(bookmarks);
                });
            }
        }

        function updateBookmarkListDisplay(bookmarks) {
            // 这里可以添加更新书签列表显示的逻辑
            console.log('📖 更新书签列表显示:', bookmarks);
        }







        // 显示通知
        function showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 10px 15px;
                border-radius: 5px;
                color: white;
                font-size: 12px;
                z-index: 10000;
                opacity: 0;
                transition: all 0.3s ease;
                ${type === 'success' ? 'background: #27ae60;' : ''}
                ${type === 'error' ? 'background: #e74c3c;' : ''}
                ${type === 'warning' ? 'background: #f39c12;' : ''}
                ${type === 'info' ? 'background: #3498db;' : ''}
            `;

            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateY(0)';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                notification.style.opacity = '0';
                notification.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }



        // 概览地图功能
        let overviewMapInitialized = false;
        let overviewMapData = null;

        function toggleMiniMap() {
            const miniMap = document.getElementById('miniMap');
            const toggleIcon = document.getElementById('miniMapToggleIcon');

            miniMap.classList.toggle('collapsed');

            if (miniMap.classList.contains('collapsed')) {
                toggleIcon.className = 'fas fa-chevron-down';
            } else {
                toggleIcon.className = 'fas fa-chevron-up';
            }
        }

        function initOverviewMap() {
            console.log('🗺️ 初始化概览地图');

            if (!communicationManager || !communicationManager.isReady) {
                alert('请等待主地图加载完成');
                return;
            }

            // 获取当前相机信息
            communicationManager.sendCommand('viewer', 'getCameraInfo').then(cameraInfo => {
                console.log('📷 获取到相机信息:', cameraInfo);

                // 创建概览地图
                createOverviewMap(cameraInfo);
                overviewMapInitialized = true;

                // 开始同步主地图视野
                startViewportSync();

            }).catch(error => {
                console.error('❌ 获取相机信息失败:', error);
                alert('初始化概览地图失败');
            });
        }

        function createOverviewMap(cameraInfo) {
            const container = document.getElementById('miniMapContainer');
            const placeholder = container.querySelector('.mini-map-placeholder');

            if (placeholder) {
                placeholder.style.display = 'none';
            }

            // 创建简化的地图显示
            container.innerHTML = `
                <div style="width: 100%; height: 100%; background: linear-gradient(45deg, #1a1a1a 25%, #2a2a2a 25%, #2a2a2a 50%, #1a1a1a 50%, #1a1a1a 75%, #2a2a2a 75%); background-size: 20px 20px; position: relative;">
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #3498db; font-size: 10px; text-align: center;">
                        <i class="fas fa-globe" style="font-size: 16px; margin-bottom: 3px; display: block;"></i>
                        <div>经度: ${cameraInfo.longitude?.toFixed(4) || 'N/A'}</div>
                        <div>纬度: ${cameraInfo.latitude?.toFixed(4) || 'N/A'}</div>
                        <div>高度: ${cameraInfo.height ? (cameraInfo.height/1000).toFixed(1) + 'km' : 'N/A'}</div>
                    </div>
                    <div class="viewport-indicator" id="viewportIndicator" style="display: block; width: 30px; height: 20px; top: 40%; left: 35%;"></div>
                </div>
            `;

            // 添加点击事件
            container.addEventListener('click', handleOverviewMapClick);
        }

        function handleOverviewMapClick(event) {
            if (!communicationManager || !communicationManager.isReady) return;

            const rect = event.currentTarget.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            // 计算相对位置（简化计算）
            const relativeX = x / rect.width;
            const relativeY = y / rect.height;

            console.log('🎯 概览地图点击位置:', { x: relativeX, y: relativeY });

            // 发送跳转命令到主地图
            communicationManager.sendCommand('viewer', 'flyToRelativePosition', {
                x: relativeX,
                y: relativeY
            }).then(() => {
                console.log('✅ 主地图跳转成功');
            }).catch(error => {
                console.error('❌ 主地图跳转失败:', error);
            });
        }

        function resetOverviewMap() {
            console.log('🏠 重置概览地图视野');

            if (!communicationManager || !communicationManager.isReady) {
                alert('请先初始化概览地图');
                return;
            }

            // 重置主地图到初始位置
            communicationManager.sendCommand('viewer', 'flyTo', {
                longitude: 104.0,
                latitude: 30.0,
                height: 17000000
            }).then(() => {
                console.log('✅ 视野重置成功');
                // 重新同步概览地图
                setTimeout(syncOverviewMap, 500);
            }).catch(error => {
                console.error('❌ 视野重置失败:', error);
            });
        }

        function syncOverviewMap() {
            console.log('🔄 同步概览地图');

            if (!overviewMapInitialized || !communicationManager || !communicationManager.isReady) {
                return;
            }

            // 获取最新的相机信息并更新概览地图
            communicationManager.sendCommand('viewer', 'getCameraInfo').then(cameraInfo => {
                updateOverviewMapDisplay(cameraInfo);
            }).catch(error => {
                console.error('❌ 同步概览地图失败:', error);
            });
        }

        function updateOverviewMapDisplay(cameraInfo) {
            const container = document.getElementById('miniMapContainer');
            const infoDiv = container.querySelector('div > div');

            if (infoDiv) {
                infoDiv.innerHTML = `
                    <i class="fas fa-globe" style="font-size: 16px; margin-bottom: 3px; display: block;"></i>
                    <div>经度: ${cameraInfo.longitude?.toFixed(4) || 'N/A'}</div>
                    <div>纬度: ${cameraInfo.latitude?.toFixed(4) || 'N/A'}</div>
                    <div>高度: ${cameraInfo.height ? (cameraInfo.height/1000).toFixed(1) + 'km' : 'N/A'}</div>
                `;
            }
        }

        function startViewportSync() {
            // 禁用自动同步以减少性能开销
            console.log('📍 概览地图自动同步已禁用');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📱 index2.html 页面加载完成');

            // 初始化坐标系选择器
            const crsSelector = document.getElementById('crsSelector');
            crsSelector.addEventListener('change', function() {
                console.log('坐标系切换到:', this.value);
                // 这里可以添加坐标系转换逻辑
            });
        });
    </script>
</body>
</html>