* {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }

        /* 顶部工具栏 */
        .top-toolbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(135deg, #2c3e50, #34495e);
            border-bottom: 1px solid #34495e;
            display: flex;
            align-items: center;
            padding: 0 15px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .logo {
            display: flex;
            align-items: center;
            margin-right: 30px;
        }

        .logo i {
            font-size: 24px;
            color: #3498db;
            margin-right: 10px;
        }

        .logo span {
            font-size: 18px;
            font-weight: bold;
            color: #ecf0f1;
        }

        .toolbar-buttons {
            display: flex;
            gap: 10px;
            flex: 1;
        }

        .toolbar-btn {
            background: rgba(255,255,255,0.1);
            border: none;
            color: #ecf0f1;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .toolbar-btn:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-1px);
        }

        .search-container {
            margin-left: auto;
            position: relative;
        }

        .search-box {
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #ecf0f1;
            padding: 8px 40px 8px 15px;
            border-radius: 20px;
            width: 300px;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            background: rgba(255,255,255,0.15);
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }

        .search-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #3498db;
            cursor: pointer;
        }

        /* 左侧面板 */
        .left-panel {
            position: fixed;
            left: 0;
            top: 50px;
            bottom: 30px;
            width: 320px;
            background: linear-gradient(180deg, #2c3e50, #34495e);
            border-right: 1px solid #34495e;
            z-index: 999;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.3);
        }

        .panel-section {
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .panel-header {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            font-weight: bold;
            color: #3498db;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background 0.3s ease;
        }

        .panel-header:hover {
            background: rgba(0,0,0,0.3);
        }

        .panel-content {
            padding: 15px;
            display: none;
        }

        .panel-content.active {
            display: block;
        }

        .layer-item, .tool-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.05);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .layer-item:hover, .tool-item:hover {
            background: rgba(255,255,255,0.05);
            transform: translateX(2px);
        }

        .tool-item.active {
            background: rgba(52, 152, 219, 0.2);
            border-left: 3px solid #3498db;
        }

        .tool-item.tool-clear {
            border-top: 1px solid rgba(255,255,255,0.1);
            margin-top: 5px;
        }

        .tool-item.tool-clear:hover {
            background: rgba(231, 76, 60, 0.2);
        }

        .layer-checkbox {
            margin-right: 10px;
        }

        .layer-icon {
            margin-right: 10px;
            color: #3498db;
            width: 16px;
            text-align: center;
        }

        .layer-name {
            flex: 1;
            font-size: 14px;
        }

        .layer-opacity {
            width: 60px;
            margin-left: 10px;
        }

        .tool-status {
            font-size: 10px;
            color: #95a5a6;
            margin-left: 5px;
        }

        .tool-status.active {
            color: #27ae60;
        }

        .tool-status.error {
            color: #e74c3c;
        }

        /* 搜索面板样式 */
        .search-panel {
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .search-input-group {
            display: flex;
            gap: 5px;
        }

        .panel-search-input {
            flex: 1;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #ecf0f1;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 12px;
            outline: none;
            transition: all 0.3s ease;
        }

        .panel-search-input:focus {
            background: rgba(255,255,255,0.15);
            border-color: #3498db;
            box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
        }

        .panel-search-btn {
            background: #3498db;
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .panel-search-btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        /* 加载指示器 */
        .loading-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            z-index: 1000;
            background: rgba(44, 62, 80, 0.9);
            padding: 30px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(52, 152, 219, 0.3);
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        .loading-text {
            color: #ecf0f1;
            font-size: 14px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 主视图区域 */
        .main-view {
            position: fixed;
            left: 320px;
            top: 50px;
            right: 0;
            bottom: 30px;
            background: #000;
        }

        #cesiumContainer {
            width: 100%;
            height: 100%;
        }



        /* 底部状态栏 */
        .bottom-status {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 35px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 15px;
            font-size: 11px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            z-index: 1000;
            color: #ffffff;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-center {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .status-right select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
        }



        /* 迷你地图 */
        .mini-map {
            position: fixed;
            bottom: 50px;
            left: 340px;
            width: 200px;
            background: rgba(44, 62, 80, 0.95);
            border: 2px solid #3498db;
            border-radius: 8px;
            z-index: 998;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .mini-map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 10px;
            background: rgba(52, 152, 219, 0.2);
            border-bottom: 1px solid rgba(52, 152, 219, 0.3);
            font-size: 11px;
            font-weight: bold;
            color: #3498db;
        }

        .mini-map-toggle {
            background: none;
            border: none;
            color: #3498db;
            cursor: pointer;
            padding: 2px;
            border-radius: 3px;
            transition: all 0.2s ease;
        }

        .mini-map-toggle:hover {
            background: rgba(52, 152, 219, 0.2);
        }

        .mini-map-content {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .mini-map.collapsed .mini-map-content {
            height: 0;
            opacity: 0;
        }

        .mini-map-placeholder {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #95a5a6;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mini-map-placeholder:hover {
            color: #3498db;
            transform: translate(-50%, -50%) scale(1.1);
        }

        .viewport-indicator {
            position: absolute;
            border: 2px solid #e74c3c;
            background: rgba(231, 76, 60, 0.2);
            pointer-events: none;
            display: none;
            z-index: 10;
        }

        .mini-map-controls {
            display: flex;
            justify-content: space-around;
            padding: 5px;
            background: rgba(0, 0, 0, 0.2);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .mini-control-btn {
            background: rgba(52, 152, 219, 0.2);
            border: 1px solid rgba(52, 152, 219, 0.3);
            color: #3498db;
            padding: 4px 6px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.2s ease;
        }

        .mini-control-btn:hover {
            background: rgba(52, 152, 219, 0.4);
            transform: translateY(-1px);
        }

        .mini-control-btn:active {
            transform: translateY(0);
        }

        /* 滚动条样式 */
        .left-panel::-webkit-scrollbar {
            width: 6px;
        }

        .left-panel::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .left-panel::-webkit-scrollbar-thumb {
            background: #3498db;
            border-radius: 3px;
        }

        /* 功能设置面板 */
        .tool-settings {
            background: rgba(0,0,0,0.3);
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid rgba(255,255,255,0.1);
            display: none;
            animation: slideDown 0.3s ease;
        }

        .tool-settings.active {
            display: block;
        }

        .settings-content {
            padding: 15px;
        }

        .setting-group {
            margin-bottom: 15px;
        }

        .setting-label {
            display: block;
            color: #bdc3c7;
            font-size: 12px;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .setting-input {
            width: 100%;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #ecf0f1;
            padding: 8px 10px;
            border-radius: 3px;
            font-size: 12px;
            outline: none;
            transition: all 0.3s ease;
        }

        .setting-input:focus {
            background: rgba(255,255,255,0.15);
            border-color: #3498db;
        }

        .setting-range {
            width: 100%;
            margin: 5px 0;
        }

        .setting-select {
            width: 100%;
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            color: #ecf0f1;
            padding: 6px 8px;
            border-radius: 3px;
            font-size: 12px;
            outline: none;
        }

        .setting-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 10px;
        }

        .setting-btn {
            background: #3498db;
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 60px;
        }

        .setting-btn:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }

        .setting-btn.secondary {
            background: #95a5a6;
        }

        .setting-btn.secondary:hover {
            background: #7f8c8d;
        }

        .setting-btn.danger {
            background: #e74c3c;
        }

        .setting-btn.danger:hover {
            background: #c0392b;
        }

        .range-value {
            color: #3498db;
            font-size: 11px;
            font-weight: bold;
        }



        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 500px;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .left-panel {
                width: 280px;
            }

            .main-view {
                left: 280px;
            }

            .search-box {
                width: 200px;
            }
        }