<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子沙盘展示</title>
    
    <!-- Cesium CDN -->
    <link href="node_modules/cesium/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    <link rel="stylesheet" href="src/features/布局/坐标导航/navigation-combined.css">
    <!-- 功能性CSS文件 - 新中文路径 -->
    <link rel="stylesheet" href="src/features/按钮/测量工具/styles/measureTool.css">
    <link rel="stylesheet" href="src/features/按钮/测量工具/styles/measure.css">
    
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="src/css/main.css">
    <link rel="stylesheet" href="src/css/title.css">
    <link rel="stylesheet" href="src/css/buttons.css">
    <link rel="stylesheet" href="src/features/按钮/搜索功能/styles/search.css">
    <link rel="stylesheet" href="src/features/按钮/地形开挖/styles/terrain-dig.css">
    <link rel="stylesheet" href="src/features/按钮/剖面分析/styles/toolbar-buttons.css">
    <link rel="stylesheet" href="src/features/按钮/书签管理/styles/bookmark.css">
    <!-- 添加roamFly样式 -->
    <link rel="stylesheet" href="src/features/按钮/漫游飞行/styles/roamFly.css">
    <!-- 添加打印功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/打印功能/styles/print.css">
    <!-- 添加标记点功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/标记管理/styles/addMarker.css">
    <!-- 添加场景管理功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/场景管理/styles/sceneManager.css">
    <!-- 添加3D建筑功能样式 -->
    <link rel="stylesheet" href="src/features/按钮/三维建筑/styles/building3d.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- JavaScript -->
    <script src="node_modules/cesium/Build/Cesium/Cesium.js"></script>
    <script src="https://unpkg.com/@turf/turf@6.5.0/turf.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- 确保工具类先加载 -->
    <script src="src/features/工具类/PanelPositioner.js"></script>
    <!-- 高价值优化系统 -->
    <script src="src/features/工具类/EventBus.js"></script>
    <script src="src/features/工具类/ButtonConfig.js"></script>
    <script src="src/features/工具类/PanelManager.js"></script>
    <script src="src/features/工具类/ButtonFactory.js"></script>
    <script src="src/features/工具类/EnhancedToolbarManager.js"></script>
    <!-- 使用示例和演示 -->
    <script src="src/features/工具类/HighValueOptimizationExamples.js"></script>
    <!-- 原有工具栏管理器（备用） -->
    <script src="src/features/工具类/ToolbarManager.js"></script>
    <script src="src/features/工具类/ToolbarManagerExample.js"></script>
    <script src="src/js/cesium.js"></script>
    <script src="src/features/布局/天空盒/SkyBoxManager.js"></script>
    <!-- 功能性JS文件 - 新中文路径 -->
    <script src="src/features/按钮/测量工具/core/MeasureTool.js"></script>
    <script src="src/features/布局/坐标导航/CesiumNavigation.umd.js"></script>
    <script src="src/features/按钮/搜索功能/core/LocationSearch.js"></script>
    <script src="src/features/布局/坐标导航/CoordinateDisplay.js"></script>
    <script src="src/features/按钮/地形开挖/core/TerrainDigHandler.js"></script>
    <script src="src/features/按钮/剖面分析/core/ProfileAnalysis.js"></script>
    <script src="src/features/按钮/书签管理/core/BookmarkTool.js"></script>
    
    <!-- UI组件JS文件 -->
    <script src="src/features/按钮/搜索功能/ui/SearchUI.js"></script>
    <script src="src/features/按钮/地形开挖/ui/TerrainDigUI.js"></script>
    <script src="src/features/按钮/剖面分析/ui/ProfileAnalysisUI.js"></script>
    <script src="src/features/按钮/测量工具/ui/MeasureUI.js"></script>
    <script src="src/features/按钮/书签管理/ui/BookmarkUI.js"></script>
    <!-- 添加roamFly模块 -->
    <script type="module" src="src/features/按钮/漫游飞行/index.js"></script>
    <!-- 添加打印功能模块 -->
    <script src="src/features/按钮/打印功能/config.js"></script>
    <script src="src/features/按钮/打印功能/core/PrintTool.js"></script>
    <script src="src/features/按钮/打印功能/ui/PrintUI.js"></script>
    <!-- 添加标记点功能模块 -->
    <script src="src/features/按钮/标记管理/core/AddMarkerTool.js"></script>
    <script src="src/features/按钮/标记管理/ui/AddMarkerUI.js"></script>
    <!-- 添加场景管理功能模块 -->
    <script type="module" src="src/features/按钮/场景管理/index.js"></script>
    <!-- 添加3D建筑功能模块 -->
    <script src="src/features/按钮/三维建筑/config.js"></script>
    <script src="src/features/按钮/三维建筑/core/Building3DTool.js"></script>
    <script src="src/features/按钮/三维建筑/ui/Building3DUI.js"></script>
</head>
<body>
    <!-- 标题图片容器 -->
    <div class="title-container">
        <img src="src/images/svg/title.svg" alt="标题">
        <div class="title-text">电子沙盘</div>
    </div>
    
    <div id="cesiumContainer"></div>
    
    <!-- 工具按钮组 - 按钮将由各UI组件动态添加 -->
    <div id="toolButtons"></div>
    
    <!-- 引入SVG图标 -->
    <div id="svg-container"></div>
    
    <script>
        // 等待页面加载完成
        window.onload = function() {
            try {
                // 初始化Cesium
                const viewer = initCesium();
                
                // 初始化坐标显示功能
                window.coordinateDisplay = new CoordinateDisplay(viewer);
                
                // 初始化UI组件
                window.searchUI = SearchUI.init(viewer, 'toolButtons');
                window.terrainDigUI = TerrainDigUI.init(viewer, 'toolButtons');
                window.profileAnalysisUI = ProfileAnalysisUI.init(viewer, 'toolButtons');
                // 异步初始化测量工具
                MeasureUI.init(viewer, 'toolButtons').then(measureUI => {
                    window.measureUI = measureUI;
                    console.log('✅ 测量工具初始化完成');
                }).catch(error => {
                    console.error('❌ 测量工具初始化失败:', error);
                });
                // 异步初始化书签管理
                BookmarkUI.init(viewer, 'toolButtons').then(bookmarkUI => {
                    window.bookmarkUI = bookmarkUI;
                    console.log('✅ 书签管理初始化完成');
                }).catch(error => {
                    console.error('❌ 书签管理初始化失败:', error);
                });
                // 初始化roamFly功能
                import('./src/features/按钮/漫游飞行/index.js').then(module => {
                    window.roamFlyUI = module.RoamFlyUI.init(viewer, 'toolButtons');
                });
                
                // 初始化打印功能
                window.printUI = PrintUI.init(viewer, 'toolButtons');
                
                // 初始化标记点功能
                window.addMarkerUI = AddMarkerUI.init(viewer, 'toolButtons');
                
                // 初始化场景管理功能
                import('./src/features/按钮/场景管理/index.js').then(module => {
                    window.sceneManagerUI = module.SceneManagerUI.init(viewer, 'toolButtons');
                });
                
                // 初始化3D建筑功能
                window.building3DUI = Building3DUI.init(viewer, 'toolButtons');
                
                // 加载SVG图标
                fetch('src/images/svg/icons.svg')
                    .then(response => response.text())
                    .then(svgContent => {
                        document.getElementById('svg-container').innerHTML = svgContent;
                    })
                    .catch(error => console.error('加载SVG图标失败:', error));
                
            } catch (error) {
                console.error('初始化失败:', error);
            }
        };

        // iframe通信支持
        window.addEventListener('message', function(event) {
            // 检查消息来源安全性
            if (event.origin !== window.location.origin) {
                return;
            }

            const { type, module, method, params, id } = event.data;
            
            if (type === 'FUNCTION_CALL') {
                console.log('🔗 接收到iframe命令:', { module, method, params });
                
                try {
                    let result = null;
                    let targetObject = null;

                    // 根据模块名找到对应的对象
                    switch (module) {
                        case 'viewer':
                            targetObject = window.viewer;
                            break;
                        case 'measureUI':
                            targetObject = window.measureUI;
                            break;
                        case 'searchUI':
                            targetObject = window.searchUI;
                            break;
                        case 'terrainDigUI':
                            targetObject = window.terrainDigUI;
                            break;
                        case 'profileAnalysisUI':
                            targetObject = window.profileAnalysisUI;
                            break;
                        case 'bookmarkUI':
                            targetObject = window.bookmarkUI;
                            break;
                        case 'addMarkerUI':
                            targetObject = window.addMarkerUI;
                            break;
                        case 'building3DUI':
                            targetObject = window.building3DUI;
                            break;
                        case 'printUI':
                            targetObject = window.printUI;
                            break;
                        default:
                            console.warn('未知模块:', module);
                            break;
                    }

                    if (targetObject && typeof targetObject[method] === 'function') {
                        result = targetObject[method](params);
                    } else {
                        throw new Error(`方法 ${module}.${method} 不存在`);
                    }

                    // 发送响应
                    event.source.postMessage({
                        type: 'RESPONSE',
                        id: id,
                        data: result
                    }, event.origin);

                } catch (error) {
                    console.error('执行命令失败:', error);
                    event.source.postMessage({
                        type: 'RESPONSE',
                        id: id,
                        error: error.message
                    }, event.origin);
                }
            }
        });

        // 当所有组件初始化完成后，通知父窗口
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'READY'
                    }, '*');
                    console.log('📡 向父窗口发送READY消息');
                }
            }, 2000); // 等待2秒确保所有组件都初始化完成
        });
    </script>
</body>
</html>
