/**
 * 书签管理UI组件
 * 提供书签管理功能的界面和事件处理
 */
class BookmarkUI {
    /**
     * 创建书签管理UI组件
     * @param {Object} viewer - Cesium viewer实例
     */
    constructor(viewer) {
        this.viewer = viewer;
        this.bookmarkTool = null;
        this.panelId = 'bookmarkPanel';
        this.toggleBtnId = 'toggleBookmark';
    }

    /**
     * 生成书签管理组件的HTML代码
     * @returns {string} HTML代码字符串
     */
    generateHTML() {
        return `
        <!-- 书签管理按钮 -->
        <button id="${this.toggleBtnId}">
            <img src="src/features/按钮/书签管理/assets/svg/bookmark.svg" alt="书签管理">
            <div class="tooltip">书签管理</div>
        </button>
        
        <!-- 书签管理面板 -->
        <div id="${this.panelId}" style="display: none;">
            <div class="toolbar-panel-title">书签管理</div>
            <div class="bookmark-controls">
                <button id="addBookmarkBtn" class="primary-btn">
                    <i class="fas fa-plus"></i>
                    添加书签
                </button>
            </div>
            <div id="bookmarkList" class="bookmark-list">
                <!-- 书签列表将在这里动态生成 -->
            </div>
        </div>`;
    }

    /**
     * 将按钮添加到工具按钮组
     * @param {string} containerId - 工具按钮组容器ID
     */
    appendButtonTo(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`未找到容器元素: ${containerId}`);
            return;
        }

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <button id="${this.toggleBtnId}">
            <img src="src/features/按钮/书签管理/assets/svg/bookmark.svg" alt="书签管理">
            <div class="tooltip">书签管理</div>
        </button>`;
        
        container.appendChild(tempDiv.firstElementChild);
        console.log(`书签管理按钮已添加到: ${containerId}`);
    }

    /**
     * 将面板添加到页面
     */
    appendPanelToBody() {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <!-- 书签管理面板 -->
        <div id="${this.panelId}" class="analysis-panel" style="display:none; width: 280px; padding: 15px;">
            <div class="toolbar-panel-title">书签管理</div>
            <div class="bookmark-add-form">
                <input type="text" id="bookmarkNameInput" placeholder="输入书签名称" class="form-control" style="width: calc(100% - 60px); display: inline-block;">
                <button id="addBookmarkBtn" class="btn btn-primary" style="width: 50px;">添加</button>
            </div>
            <div id="bookmarkList" class="bookmark-list" style="margin-top: 15px; max-height: 350px; overflow-y: auto;">
                <!-- 书签项将动态添加到这里 -->
            </div>
        </div>`;
        
        document.body.appendChild(tempDiv.firstElementChild);
        console.log('书签管理面板已添加到页面');
    }

    /**
     * 渲染书签列表
     */
    renderBookmarkList() {
        if (!this.bookmarkTool) return;
        
        const bookmarks = this.bookmarkTool.getAllBookmarks();
        const listContainer = document.getElementById('bookmarkList');
        if (!listContainer) return;
        
        listContainer.innerHTML = '';
        
        if (bookmarks.length === 0) {
            listContainer.innerHTML = '<div class="empty-message">没有保存的书签</div>';
            return;
        }
        
        bookmarks.forEach(bookmark => {
            const bookmarkItem = document.createElement('div');
            bookmarkItem.className = 'bookmark-item';
            bookmarkItem.dataset.id = bookmark.id;
            
            let imageHtml = '';
            if (bookmark.icon) {
                imageHtml = `<img src="${bookmark.icon}" alt="${bookmark.name}" class="bookmark-thumbnail">`;
            } else {
                imageHtml = `<div class="bookmark-thumbnail-placeholder">无预览图</div>`;
            }
            
            bookmarkItem.innerHTML = `
                <div class="bookmark-preview">${imageHtml}</div>
                <div class="bookmark-info">
                    <div class="bookmark-name">${bookmark.name}</div>
                    <div class="bookmark-date">${new Date(bookmark.id).toLocaleString()}</div>
                </div>
                <div class="bookmark-actions">
                    <button class="btn-fly" title="飞行到此位置"><i class="fas fa-paper-plane"></i></button>
                    <button class="btn-delete" title="删除书签"><i class="fas fa-trash"></i></button>
                </div>
            `;
            
            listContainer.appendChild(bookmarkItem);
            
            // 添加事件监听
            const flyBtn = bookmarkItem.querySelector('.btn-fly');
            flyBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.bookmarkTool.flyToBookmark(bookmark);
            });
            
            const deleteBtn = bookmarkItem.querySelector('.btn-delete');
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.bookmarkTool.deleteBookmark(bookmark.id);
                this.renderBookmarkList();
            });
            
            // 点击整个条目也可以飞行
            bookmarkItem.addEventListener('click', () => {
                this.bookmarkTool.flyToBookmark(bookmark);
            });
        });
    }

    /**
     * 初始化书签管理组件及事件绑定
     */
    async init() {
        console.log('正在初始化书签管理UI组件...');
        
        // 等待BookmarkTool加载完成
        if (typeof BookmarkTool === 'undefined') {
            console.log('BookmarkTool未定义，等待加载...');
            await this.waitForBookmarkTool();
        }
        
        // 获取或创建书签工具实例
        if (!this.bookmarkTool) {
            this.bookmarkTool = new BookmarkTool(this.viewer);
            window.bookmarkTool = this.bookmarkTool;
        }
        
        // 绑定事件
        const toggleBtn = document.getElementById(this.toggleBtnId);
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.togglePanel();
            });
        }
        
        const addBookmarkBtn = document.getElementById('addBookmarkBtn');
        if (addBookmarkBtn) {
            addBookmarkBtn.addEventListener('click', () => {
                this.addBookmark();
            });
        }
        
        const bookmarkNameInput = document.getElementById('bookmarkNameInput');
        if (bookmarkNameInput) {
            bookmarkNameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addBookmark();
                }
            });
        }
        
        console.log('书签管理UI组件初始化完成');
    }

    /**
     * 添加新书签
     */
    async addBookmark() {
        const input = document.getElementById('bookmarkNameInput');
        if (!input) return;
        
        const name = input.value.trim();
        if (!name) {
            alert('请输入书签名称');
            return;
        }
        
        try {
            await this.bookmarkTool.addBookmark(name);
            input.value = ''; // 清空输入框
            this.renderBookmarkList(); // 重新渲染列表
        } catch (error) {
            alert(error.message);
        }
    }

    /**
     * 切换面板显示状态
     */
    togglePanel() {
        const panel = document.getElementById(this.panelId);
        const toggleBtn = document.getElementById(this.toggleBtnId);
        
        if (!panel) return;
        
        if (panel.style.display === 'none' || !panel.style.display) {
            this.showPanel();
        } else {
            this.hidePanel();
        }
    }

    /**
     * 显示面板并设置位置
     */
    showPanel() {
        const panel = document.getElementById(this.panelId);
        const toggleBtn = document.getElementById(this.toggleBtnId);
        
        if (panel) {
            // 隐藏其他面板
            const otherPanels = ['searchPanel', 'measureToolContainer', 'terrainDigPanel', 'profileAnalysisPanel'];
            otherPanels.forEach(panelId => {
                const otherPanel = document.getElementById(panelId);
                if (otherPanel) {
                    otherPanel.style.display = 'none';
                }
            });
            
            panel.style.display = 'block';
            
            if (toggleBtn && typeof PanelPositioner !== 'undefined') {
                try {
                    PanelPositioner.setPosition(toggleBtn, panel, {
                        preferredPosition: 'right',
                        gap: 10
                    });
                } catch (e) {
                    console.error('面板定位出错:', e);
                    // 默认位置
                    panel.style.position = 'fixed';
                    panel.style.top = '100px';
                    panel.style.left = '100px';
                }
            } else {
                // 默认位置
                panel.style.position = 'fixed';
                panel.style.top = '100px';
                panel.style.left = '100px';
            }
            
            // 渲染书签列表
            this.renderBookmarkList();
        }
    }

    /**
     * 隐藏面板
     */
    hidePanel() {
        const panel = document.getElementById(this.panelId);
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * 等待BookmarkTool类加载完成
     */
    waitForBookmarkTool() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 50; // 最多等待5秒
            
            const checkInterval = setInterval(() => {
                attempts++;
                if (typeof BookmarkTool !== 'undefined') {
                    console.log('BookmarkTool已加载完成');
                    clearInterval(checkInterval);
                    resolve();
                } else if (attempts >= maxAttempts) {
                    console.error('BookmarkTool加载超时');
                    clearInterval(checkInterval);
                    reject(new Error('BookmarkTool加载超时'));
                }
            }, 100);
        });
    }

    /**
     * 静态初始化方法
     * @param {Object} viewer - Cesium viewer实例
     * @param {string} toolButtonsId - 工具按钮容器ID
     * @returns {Promise<BookmarkUI>} 书签管理UI实例
     */
    static async init(viewer, toolButtonsId = 'toolButtons') {
        console.log('BookmarkUI.init 被调用');
        const bookmarkUI = new BookmarkUI(viewer);
        
        try {
            // 检查是否需要添加HTML元素
            if (!document.getElementById(bookmarkUI.toggleBtnId)) {
                bookmarkUI.appendButtonTo(toolButtonsId);
            }
            
            if (!document.getElementById(bookmarkUI.panelId)) {
                bookmarkUI.appendPanelToBody();
            }
            
            // 异步初始化组件
            await bookmarkUI.init();
            
            return bookmarkUI;
        } catch (error) {
            console.error('BookmarkUI静态初始化失败:', error);
            return bookmarkUI; // 即使失败也返回实例，便于调试
        }
    }
}

// 导出到全局作用域
window.BookmarkUI = BookmarkUI; 