/**
 * 测量工具UI组件
 * 提供测量工具功能的界面和事件处理
 */
class MeasureUI {
    /**
     * 创建测量工具UI组件
     * @param {Object} viewer - Cesium viewer实例
     */
    constructor(viewer) {
        this.viewer = viewer;
        this.measureTool = null;
        this.panelId = 'measureToolContainer';
        this.toggleBtnId = 'toggleMeasure';
    }

    /**
     * 生成测量工具组件的HTML代码
     * @returns {string} HTML代码字符串
     */
    generateHTML() {
        return `
        <!-- 测量工具按钮 -->
        <button id="${this.toggleBtnId}">
            <img src="src/features/按钮/测量工具/assets/svg/measure.svg" alt="测量工具">
            <div class="tooltip">测量工具</div>
        </button>
        
        <!-- 测量工具面板 -->
        <div id="${this.panelId}">
            <div class="toolbar-panel-title">测量工具</div>
            <div class="button-group">
                <button id="measureDistance" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/distance.svg" alt="距离">
                    距离测量
                </button>
                <button id="measureArea" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/area.svg" alt="面积">
                    面积测量
                </button>
                <button id="measureHeight" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/height.svg" alt="高度">
                    高度测量
                </button>
                <button id="measureSpaceDistance" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/space-distance.svg" alt="空间">
                    空间距离
                </button>
                <button id="measureTriangle" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/triangle.svg" alt="三角">
                    三角测量
                </button>
                <button id="measureSquareAngle" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/angle.svg" alt="方位角">
                    方位角
                </button>
                <button id="clearMeasure" class="measure-btn clear-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/clear.svg" alt="清除">
                    清除结果
                </button>
            </div>
        </div>`;
    }

    /**
     * 将按钮添加到工具按钮组
     * @param {string} containerId - 工具按钮组容器ID
     */
    appendButtonTo(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`未找到容器元素: ${containerId}`);
            return;
        }

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <button id="${this.toggleBtnId}">
            <img src="src/features/按钮/测量工具/assets/svg/measure.svg" alt="测量工具">
            <div class="tooltip">测量工具</div>
        </button>`;
        
        container.appendChild(tempDiv.firstElementChild);
        console.log(`测量工具按钮已添加到: ${containerId}`);
    }

    /**
     * 将面板添加到页面
     */
    appendPanelToBody() {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = `
        <!-- 测量工具面板 -->
        <div id="${this.panelId}" style="display:none;">
            <div class="toolbar-panel-title">测量工具</div>
            <div class="button-group">
                <button id="measureDistance" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/distance.svg" alt="距离">
                    距离测量
                </button>
                <button id="measureArea" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/area.svg" alt="面积">
                    面积测量
                </button>
                <button id="measureHeight" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/height.svg" alt="高度">
                    高度测量
                </button>
                <button id="measureSpaceDistance" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/space-distance.svg" alt="空间">
                    空间距离
                </button>
                <button id="measureTriangle" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/triangle.svg" alt="三角">
                    三角测量
                </button>
                <button id="measureSquareAngle" class="measure-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/angle.svg" alt="方位角">
                    方位角
                </button>
                <button id="clearMeasure" class="measure-btn clear-btn">
                    <img src="src/features/按钮/测量工具/assets/svg/clear.svg" alt="清除">
                    清除结果
                </button>
            </div>
        </div>`;
        
        document.body.appendChild(tempDiv.firstElementChild);
        console.log('测量工具面板已添加到页面');
    }

    /**
     * 初始化测量工具组件及事件绑定
     */
    async init() {
        console.log('正在初始化测量工具UI组件...');
        
        try {
            // 获取或创建测量工具实例
            if (!this.measureTool) {
                this.measureTool = await initMeasureTool(this.viewer, this.panelId, null, this.toggleBtnId);
                window.measureTool = this.measureTool;
            }
            
            // 创建面板切换函数（由于MeasureTool.js已经自带切换功能，这里不需要重复实现）
            
            console.log('测量工具UI组件初始化完成');
        } catch (error) {
            console.error('测量工具初始化失败:', error);
            throw error;
        }
    }

    /**
     * 显示面板并设置位置
     */
    showPanel() {
        const panel = document.getElementById(this.panelId);
        const toggleBtn = document.getElementById(this.toggleBtnId);
        
        if (panel) {
            panel.style.display = 'block';
            
            if (toggleBtn && typeof PanelPositioner !== 'undefined') {
                try {
                    PanelPositioner.setPosition(toggleBtn, panel, {
                        preferredPosition: 'right',
                        gap: 10
                    });
                } catch (e) {
                    console.error('面板定位出错:', e);
                    // 默认位置
                    panel.style.position = 'fixed';
                    panel.style.top = '100px';
                    panel.style.left = '100px';
                }
            } else {
                // 默认位置
                panel.style.position = 'fixed';
                panel.style.top = '100px';
                panel.style.left = '100px';
            }
        }
    }

    /**
     * 隐藏面板
     */
    hidePanel() {
        const panel = document.getElementById(this.panelId);
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * 静态初始化方法
     * @param {Object} viewer - Cesium viewer实例
     * @param {string} toolButtonsId - 工具按钮容器ID
     * @returns {Promise<MeasureUI>} 测量工具UI实例
     */
    static async init(viewer, toolButtonsId = 'toolButtons') {
        console.log('MeasureUI.init 被调用');
        const measureUI = new MeasureUI(viewer);
        
        try {
            // 检查是否需要添加HTML元素
            if (!document.getElementById(measureUI.toggleBtnId)) {
                measureUI.appendButtonTo(toolButtonsId);
            }
            
            if (!document.getElementById(measureUI.panelId)) {
                measureUI.appendPanelToBody();
            }
            
            // 初始化测量工具组件
            await measureUI.init();
            
            return measureUI;
        } catch (error) {
            console.error('MeasureUI静态初始化失败:', error);
            return measureUI; // 即使失败也返回实例，便于调试
        }
    }
}

// 导出到全局作用域
window.MeasureUI = MeasureUI; 